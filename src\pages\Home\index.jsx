import React from 'react'
import { <PERSON> } from 'react-router-dom'
import ROUTES from '@constants/routes'

const Home = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>AutoService Dashboard</h1>
      <p>Welcome to the Vehicle Management System</p>

      <div style={{ marginTop: '30px' }}>
        <h3>Authentication Pages:</h3>
        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginTop: '20px' }}>
          <Link
            to={ROUTES.LOGIN}
            style={{
              padding: '10px 20px',
              backgroundColor: '#FF6B35',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: '600'
            }}
          >
            Login Page
          </Link>
          <Link
            to={ROUTES.REGISTER}
            style={{
              padding: '10px 20px',
              backgroundColor: '#1A1A1A',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: '600'
            }}
          >
            Register Page
          </Link>
          <Link
            to={ROUTES.FORGOT_PASSWORD}
            style={{
              padding: '10px 20px',
              backgroundColor: '#C0C0C0',
              color: '#1A1A1A',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: '600'
            }}
          >
            Forgot Password
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Home