import React from 'react'
import { <PERSON> } from 'react-router-dom'
import ROUTES from '@constants/routes'

const Home = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>AutoService Dashboard</h1>
      <p>Welcome to the Vehicle Management System</p>

      <div style={{ marginTop: '30px' }}>
        <h3>🚀 Cutting-Edge 3D Authentication:</h3>
        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginTop: '20px', flexWrap: 'wrap' }}>
          <Link
            to={ROUTES.AUTH_3D}
            style={{
              padding: '20px 40px',
              background: 'linear-gradient(135deg, #00ffff 0%, #ff6b35 50%, #39ff14 100%)',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '15px',
              fontWeight: '700',
              fontSize: '1.2rem',
              textTransform: 'uppercase',
              letterSpacing: '2px',
              boxShadow: '0 15px 40px rgba(0, 255, 255, 0.4)',
              transition: 'all 0.4s ease',
              border: 'none',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-5px) scale(1.05)';
              e.target.style.boxShadow = '0 20px 50px rgba(0, 255, 255, 0.6)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0) scale(1)';
              e.target.style.boxShadow = '0 15px 40px rgba(0, 255, 255, 0.4)';
            }}
          >
            🎮 3D Immersive Auth Experience
          </Link>
        </div>

        <div style={{ marginTop: '30px' }}>
          <h4>🎬 Standard Unified Interface:</h4>
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginTop: '15px', flexWrap: 'wrap' }}>
            <Link
              to={ROUTES.AUTH}
              style={{
                padding: '12px 25px',
                background: 'linear-gradient(135deg, #FF6B35 0%, #E55A2B 100%)',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '10px',
                fontWeight: '600',
                fontSize: '1rem',
                textTransform: 'uppercase',
                letterSpacing: '1px',
                boxShadow: '0 8px 25px rgba(255, 107, 53, 0.3)',
                transition: 'all 0.3s ease',
                border: 'none'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-3px)';
                e.target.style.boxShadow = '0 12px 35px rgba(255, 107, 53, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 25px rgba(255, 107, 53, 0.3)';
              }}
            >
              Unified Auth Experience
            </Link>
          </div>
        </div>

        <div style={{ marginTop: '40px' }}>
          <h4 style={{ color: '#666', marginBottom: '15px' }}>Individual Pages (Legacy):</h4>
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link
              to={ROUTES.LOGIN}
              style={{
                padding: '8px 16px',
                backgroundColor: '#FF6B35',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '6px',
                fontWeight: '600',
                fontSize: '0.9rem'
              }}
            >
              Login
            </Link>
            <Link
              to={ROUTES.REGISTER}
              style={{
                padding: '8px 16px',
                backgroundColor: '#1A1A1A',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '6px',
                fontWeight: '600',
                fontSize: '0.9rem'
              }}
            >
              Register
            </Link>
            <Link
              to={ROUTES.FORGOT_PASSWORD}
              style={{
                padding: '8px 16px',
                backgroundColor: '#C0C0C0',
                color: '#1A1A1A',
                textDecoration: 'none',
                borderRadius: '6px',
                fontWeight: '600',
                fontSize: '0.9rem'
              }}
            >
              Forgot Password
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home