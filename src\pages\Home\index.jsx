import React from 'react'
import { <PERSON> } from 'react-router-dom'
import ROUTES from '@constants/routes'

const Home = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>AutoService Dashboard</h1>
      <p>Welcome to the Vehicle Management System</p>

      <div style={{ marginTop: '30px' }}>
        <h3>🚗 Unified Authentication Interface:</h3>
        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginTop: '20px', flexWrap: 'wrap' }}>
          <Link
            to={ROUTES.AUTH}
            style={{
              padding: '15px 30px',
              background: 'linear-gradient(135deg, #FF6B35 0%, #E55A2B 100%)',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '12px',
              fontWeight: '700',
              fontSize: '1.1rem',
              textTransform: 'uppercase',
              letterSpacing: '1px',
              boxShadow: '0 8px 25px rgba(255, 107, 53, 0.3)',
              transition: 'all 0.3s ease',
              border: 'none'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-3px)';
              e.target.style.boxShadow = '0 12px 35px rgba(255, 107, 53, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 8px 25px rgba(255, 107, 53, 0.3)';
            }}
          >
            🎬 Unified Auth Experience
          </Link>
        </div>

        <div style={{ marginTop: '40px' }}>
          <h4 style={{ color: '#666', marginBottom: '15px' }}>Individual Pages (Legacy):</h4>
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link
              to={ROUTES.LOGIN}
              style={{
                padding: '8px 16px',
                backgroundColor: '#FF6B35',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '6px',
                fontWeight: '600',
                fontSize: '0.9rem'
              }}
            >
              Login
            </Link>
            <Link
              to={ROUTES.REGISTER}
              style={{
                padding: '8px 16px',
                backgroundColor: '#1A1A1A',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '6px',
                fontWeight: '600',
                fontSize: '0.9rem'
              }}
            >
              Register
            </Link>
            <Link
              to={ROUTES.FORGOT_PASSWORD}
              style={{
                padding: '8px 16px',
                backgroundColor: '#C0C0C0',
                color: '#1A1A1A',
                textDecoration: 'none',
                borderRadius: '6px',
                fontWeight: '600',
                fontSize: '0.9rem'
              }}
            >
              Forgot Password
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home