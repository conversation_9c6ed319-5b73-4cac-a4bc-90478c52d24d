import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { gsap } from "gsap";
import { FaEye, FaEyeSlash, FaUser, FaLock, FaCar, FaEnvelope, FaPhone, FaArrowLeft, FaCheckCircle } from "react-icons/fa";
import ROUTES from "@constants/routes";

const Auth = () => {
  const [currentForm, setCurrentForm] = useState('login'); // 'login', 'register', 'forgot'
  const [isAnimating, setIsAnimating] = useState(false);
  const [formData, setFormData] = useState({
    // Login
    email: "",
    password: "",
    // Register
    firstName: "",
    lastName: "",
    phone: "",
    confirmPassword: "",
    // Forgot Password
    resetEmail: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [resetSuccess, setResetSuccess] = useState(false);

  const cardRef = useRef(null);
  const logoRef = useRef(null);
  const formContainerRef = useRef(null);
  const loginFormRef = useRef(null);
  const registerFormRef = useRef(null);
  const forgotFormRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Initial entrance animation
    const tl = gsap.timeline({ delay: 0.3 });

    // Animate card entrance
    tl.fromTo(cardRef.current,
      { scale: 0.8, opacity: 0, y: 50 },
      { scale: 1, opacity: 1, y: 0, duration: 1, ease: "back.out(1.7)" }
    );

    // Animate logo
    tl.fromTo(logoRef.current,
      { y: -30, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" },
      "-=0.6"
    );

    // Animate initial form
    tl.fromTo(loginFormRef.current,
      { x: -30, opacity: 0 },
      { x: 0, opacity: 1, duration: 0.6, ease: "power2.out" },
      "-=0.4"
    );

    // Floating animation for the car icon
    gsap.to(".car-icon", {
      y: -10,
      duration: 2.5,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    });

  }, []);

  const switchForm = (newForm, direction = 'left') => {
    if (isAnimating || currentForm === newForm) return;

    setIsAnimating(true);
    setErrors({});
    setResetSuccess(false);

    const currentFormRef = getCurrentFormRef();
    const newFormRef = getFormRef(newForm);

    const tl = gsap.timeline({
      onComplete: () => {
        setCurrentForm(newForm);
        setIsAnimating(false);
      }
    });

    // Animate current form out
    tl.to(currentFormRef.current, {
      x: direction === 'left' ? -50 : 50,
      opacity: 0,
      scale: 0.95,
      duration: 0.4,
      ease: "power2.inOut"
    });

    // Animate new form in
    tl.fromTo(newFormRef.current,
      {
        x: direction === 'left' ? 50 : -50,
        opacity: 0,
        scale: 0.95
      },
      {
        x: 0,
        opacity: 1,
        scale: 1,
        duration: 0.5,
        ease: "power2.out"
      },
      "-=0.2"
    );

    // Animate container height if needed
    const newHeight = newFormRef.current.offsetHeight;
    tl.to(formContainerRef.current, {
      height: newHeight,
      duration: 0.4,
      ease: "power2.inOut"
    }, "-=0.5");
  };

  const getCurrentFormRef = () => {
    switch (currentForm) {
      case 'login': return loginFormRef;
      case 'register': return registerFormRef;
      case 'forgot': return forgotFormRef;
      default: return loginFormRef;
    }
  };

  const getFormRef = (form) => {
    switch (form) {
      case 'login': return loginFormRef;
      case 'register': return registerFormRef;
      case 'forgot': return forgotFormRef;
      default: return loginFormRef;
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateLogin = () => {
    const newErrors = {};
    if (!formData.email) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid";
    if (!formData.password) newErrors.password = "Password is required";
    else if (formData.password.length < 6) newErrors.password = "Password must be at least 6 characters";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateRegister = () => {
    const newErrors = {};
    if (!formData.firstName) newErrors.firstName = "First name is required";
    if (!formData.lastName) newErrors.lastName = "Last name is required";
    if (!formData.email) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid";
    if (!formData.phone) newErrors.phone = "Phone number is required";
    if (!formData.password) newErrors.password = "Password is required";
    else if (formData.password.length < 6) newErrors.password = "Password must be at least 6 characters";
    if (!formData.confirmPassword) newErrors.confirmPassword = "Please confirm your password";
    else if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = "Passwords do not match";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateForgot = () => {
    const newErrors = {};
    if (!formData.resetEmail) newErrors.resetEmail = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.resetEmail)) newErrors.resetEmail = "Email is invalid";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    let isValid = false;
    switch (currentForm) {
      case 'login': isValid = validateLogin(); break;
      case 'register': isValid = validateRegister(); break;
      case 'forgot': isValid = validateForgot(); break;
    }

    if (!isValid) {
      gsap.to(cardRef.current, {
        x: [-10, 10, -10, 10, 0],
        duration: 0.5,
        ease: "power2.out"
      });
      return;
    }

    setIsLoading(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (currentForm === 'forgot') {
        setResetSuccess(true);
        gsap.fromTo(".success-content",
          { scale: 0.8, opacity: 0 },
          { scale: 1, opacity: 1, duration: 0.6, ease: "back.out(1.7)" }
        );
      } else {
        gsap.to(cardRef.current, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out",
          yoyo: true,
          repeat: 1,
          onComplete: () => {
            if (currentForm === 'login') {
              navigate(ROUTES.DASHBOARD);
            } else {
              switchForm('login');
            }
          }
        });
      }
    } catch (error) {
      console.error("Form submission failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = (field) => {
    if (field === 'password') {
      setShowPassword(!showPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }

    gsap.to(`.${field}-icon`, {
      scale: 0.8,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: "power2.out"
    });
  };

  return (
    <div className="auth-layout">
      <div className="auth-container">
        <div className="unified-auth-card" ref={cardRef}>
          <div className="auth-logo" ref={logoRef}>
            <FaCar className="car-icon" style={{ fontSize: "3.5rem", color: "var(--primary-orange)", marginBottom: "15px" }} />
            <h1>AutoService</h1>
            <p>Professional Vehicle Management</p>
          </div>

          <div className="form-container" ref={formContainerRef}>
            {/* Login Form */}
            <div
              className={`auth-form-wrapper ${currentForm === 'login' ? 'active' : 'hidden'}`}
              ref={loginFormRef}
            >
              <form className="auth-form" onSubmit={handleSubmit}>
                <div className="form-header">
                  <h2>Welcome Back</h2>
                  <p>Sign in to your account</p>
                </div>

                <div className="form-group">
                  <label className="form-label" htmlFor="email">
                    <FaUser style={{ marginRight: "8px" }} />
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className="form-control"
                    placeholder="Enter your email"
                    value={formData.email}
                    onChange={handleInputChange}
                    autoComplete="email"
                  />
                  {errors.email && (
                    <div className="error-message">
                      <span>⚠️</span>
                      {errors.email}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label className="form-label" htmlFor="password">
                    <FaLock style={{ marginRight: "8px" }} />
                    Password
                  </label>
                  <div className="input-group">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      className="form-control"
                      placeholder="Enter your password"
                      value={formData.password}
                      onChange={handleInputChange}
                      autoComplete="current-password"
                    />
                    <span
                      className="input-icon password-icon"
                      onClick={() => togglePasswordVisibility('password')}
                    >
                      {showPassword ? <FaEyeSlash /> : <FaEye />}
                    </span>
                  </div>
                  {errors.password && (
                    <div className="error-message">
                      <span>⚠️</span>
                      {errors.password}
                    </div>
                  )}
                </div>

                <button
                  type="submit"
                  className="btn-primary"
                  disabled={isLoading || isAnimating}
                >
                  {isLoading && <div className="loading-spinner"></div>}
                  {isLoading ? "Signing In..." : "Sign In"}
                </button>

                <div className="form-links">
                  <button
                    type="button"
                    className="link-button"
                    onClick={() => switchForm('forgot', 'left')}
                    disabled={isAnimating}
                  >
                    Forgot your password?
                  </button>
                </div>

                <div className="auth-divider">
                  <span>New to AutoService?</span>
                </div>

                <button
                  type="button"
                  className="btn-secondary"
                  onClick={() => switchForm('register', 'left')}
                  disabled={isAnimating}
                >
                  Create Account
                </button>
              </form>
            </div>

            {/* Register Form */}
            <div
              className={`auth-form-wrapper ${currentForm === 'register' ? 'active' : 'hidden'}`}
              ref={registerFormRef}
            >
              <form className="auth-form" onSubmit={handleSubmit}>
                <div className="form-header">
                  <h2>Create Account</h2>
                  <p>Join our professional network</p>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label" htmlFor="firstName">
                      <FaUser style={{ marginRight: "8px" }} />
                      First Name
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      className="form-control"
                      placeholder="First name"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      autoComplete="given-name"
                    />
                    {errors.firstName && (
                      <div className="error-message">
                        <span>⚠️</span>
                        {errors.firstName}
                      </div>
                    )}
                  </div>

                  <div className="form-group">
                    <label className="form-label" htmlFor="lastName">
                      Last Name
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      className="form-control"
                      placeholder="Last name"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      autoComplete="family-name"
                    />
                    {errors.lastName && (
                      <div className="error-message">
                        <span>⚠️</span>
                        {errors.lastName}
                      </div>
                    )}
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label" htmlFor="email">
                    <FaEnvelope style={{ marginRight: "8px" }} />
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className="form-control"
                    placeholder="Enter your email"
                    value={formData.email}
                    onChange={handleInputChange}
                    autoComplete="email"
                  />
                  {errors.email && (
                    <div className="error-message">
                      <span>⚠️</span>
                      {errors.email}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label className="form-label" htmlFor="phone">
                    <FaPhone style={{ marginRight: "8px" }} />
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    className="form-control"
                    placeholder="Enter your phone number"
                    value={formData.phone}
                    onChange={handleInputChange}
                    autoComplete="tel"
                  />
                  {errors.phone && (
                    <div className="error-message">
                      <span>⚠️</span>
                      {errors.phone}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label className="form-label" htmlFor="password">
                    <FaLock style={{ marginRight: "8px" }} />
                    Password
                  </label>
                  <div className="input-group">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      className="form-control"
                      placeholder="Create a password"
                      value={formData.password}
                      onChange={handleInputChange}
                      autoComplete="new-password"
                    />
                    <span
                      className="input-icon password-icon"
                      onClick={() => togglePasswordVisibility('password')}
                    >
                      {showPassword ? <FaEyeSlash /> : <FaEye />}
                    </span>
                  </div>
                  {errors.password && (
                    <div className="error-message">
                      <span>⚠️</span>
                      {errors.password}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <label className="form-label" htmlFor="confirmPassword">
                    Confirm Password
                  </label>
                  <div className="input-group">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      id="confirmPassword"
                      name="confirmPassword"
                      className="form-control"
                      placeholder="Confirm your password"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      autoComplete="new-password"
                    />
                    <span
                      className="input-icon confirmPassword-icon"
                      onClick={() => togglePasswordVisibility('confirmPassword')}
                    >
                      {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                    </span>
                  </div>
                  {errors.confirmPassword && (
                    <div className="error-message">
                      <span>⚠️</span>
                      {errors.confirmPassword}
                    </div>
                  )}
                </div>

                <button
                  type="submit"
                  className="btn-primary"
                  disabled={isLoading || isAnimating}
                >
                  {isLoading && <div className="loading-spinner"></div>}
                  {isLoading ? "Creating Account..." : "Create Account"}
                </button>

                <div className="auth-divider">
                  <span>Already have an account?</span>
                </div>

                <button
                  type="button"
                  className="btn-secondary"
                  onClick={() => switchForm('login', 'right')}
                  disabled={isAnimating}
                >
                  Sign In
                </button>
              </form>
            </div>

            {/* Forgot Password Form */}
            <div
              className={`auth-form-wrapper ${currentForm === 'forgot' ? 'active' : 'hidden'}`}
              ref={forgotFormRef}
            >
              {!resetSuccess ? (
                <form className="auth-form" onSubmit={handleSubmit}>
                  <div className="form-header">
                    <h2>Reset Password</h2>
                    <p>Enter your email to receive a reset link</p>
                  </div>

                  <div className="form-group">
                    <label className="form-label" htmlFor="resetEmail">
                      <FaEnvelope style={{ marginRight: "8px" }} />
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="resetEmail"
                      name="resetEmail"
                      className="form-control"
                      placeholder="Enter your email"
                      value={formData.resetEmail}
                      onChange={handleInputChange}
                      autoComplete="email"
                      autoFocus
                    />
                    {errors.resetEmail && (
                      <div className="error-message">
                        <span>⚠️</span>
                        {errors.resetEmail}
                      </div>
                    )}
                  </div>

                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={isLoading || isAnimating}
                  >
                    {isLoading && <div className="loading-spinner"></div>}
                    {isLoading ? "Sending Reset Link..." : "Send Reset Link"}
                  </button>

                  <div className="form-links">
                    <button
                      type="button"
                      className="link-button back-button"
                      onClick={() => switchForm('login', 'right')}
                      disabled={isAnimating}
                    >
                      <FaArrowLeft style={{ marginRight: "8px" }} />
                      Back to Sign In
                    </button>
                  </div>

                  <div className="auth-divider">
                    <span>Don't have an account?</span>
                  </div>

                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={() => switchForm('register', 'left')}
                    disabled={isAnimating}
                  >
                    Create Account
                  </button>
                </form>
              ) : (
                <div className="success-content">
                  <div className="success-icon">
                    <FaCheckCircle />
                  </div>
                  <h2>Reset Link Sent!</h2>
                  <p>
                    We've sent a password reset link to <strong>{formData.resetEmail}</strong>.
                    Please check your email and follow the instructions.
                  </p>

                  <div className="success-actions">
                    <button
                      type="button"
                      className="btn-secondary"
                      onClick={() => {
                        setResetSuccess(false);
                        setFormData(prev => ({ ...prev, resetEmail: "" }));
                      }}
                    >
                      Try Again
                    </button>

                    <button
                      type="button"
                      className="btn-primary"
                      onClick={() => switchForm('login', 'right')}
                    >
                      Back to Sign In
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Auth;
