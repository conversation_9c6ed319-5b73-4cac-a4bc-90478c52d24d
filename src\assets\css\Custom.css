/* Automotive Service Theme - Orange + Black + Silver */
:root {
  --primary-orange: #FF6B35;
  --primary-black: #1A1A1A;
  --primary-silver: #C0C0C0;
  --dark-silver: #A0A0A0;
  --light-silver: #E8E8E8;
  --accent-orange: #FF8C42;
  --dark-orange: #E55A2B;
  --white: #FFFFFF;
  --error-red: #DC3545;
  --success-green: #28A745;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, var(--primary-black) 0%, #2A2A2A 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Auth Layout Styles */
.auth-layout {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-black) 0%, #2A2A2A 50%, var(--primary-black) 100%);
  position: relative;
  overflow: hidden;
}

.auth-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.1;
  z-index: 1;
}

.auth-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
  padding: 20px;
}

.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 107, 53, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(192, 192, 192, 0.2);
  position: relative;
  overflow: hidden;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-orange) 0%, var(--accent-orange) 50%, var(--primary-orange) 100%);
  border-radius: 20px 20px 0 0;
}

/* Logo and Branding */
.auth-logo {
  text-align: center;
  margin-bottom: 30px;
}

.auth-logo h1 {
  color: var(--primary-black);
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 5px;
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--dark-orange) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-logo p {
  color: var(--dark-silver);
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Form Styles */
.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 25px;
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: var(--primary-black);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-control {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid var(--light-silver);
  border-radius: 12px;
  font-size: 1rem;
  background: var(--white);
  color: var(--primary-black);
  transition: all 0.3s ease;
  position: relative;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
  transform: translateY(-2px);
  animation: glow 3s infinite;
}

.form-control::placeholder {
  color: var(--dark-silver);
  font-weight: 400;
}

/* Input Icons */
.input-group {
  position: relative;
}

.input-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dark-silver);
  font-size: 1.2rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.input-icon:hover {
  color: var(--primary-orange);
}

/* Buttons */
.btn-primary {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--dark-orange) 100%);
  border: none;
  border-radius: 12px;
  color: var(--white);
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
  animation: glow 2s infinite;
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  width: 100%;
  padding: 15px;
  background: transparent;
  border: 2px solid var(--primary-silver);
  border-radius: 12px;
  color: var(--primary-black);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
}

.btn-secondary:hover {
  background: var(--primary-silver);
  color: var(--white);
  transform: translateY(-2px);
}

/* Links */
.auth-links {
  text-align: center;
  margin-top: 25px;
}

.auth-link {
  color: var(--primary-orange);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
}

.auth-link:hover {
  color: var(--dark-orange);
  text-decoration: none;
}

.auth-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-orange);
  transition: width 0.3s ease;
}

.auth-link:hover::after {
  width: 100%;
}

/* Divider */
.auth-divider {
  text-align: center;
  margin: 30px 0;
  position: relative;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--light-silver);
}

.auth-divider span {
  background: var(--white);
  padding: 0 20px;
  color: var(--dark-silver);
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* Error Messages */
.error-message {
  color: var(--error-red);
  font-size: 0.85rem;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Success Messages */
.success-message {
  color: var(--success-green);
  font-size: 0.85rem;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(255, 107, 53, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 107, 53, 0.6); }
  100% { box-shadow: 0 0 5px rgba(255, 107, 53, 0.3); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    padding: 15px;
  }

  .auth-card {
    padding: 30px 25px;
    border-radius: 15px;
  }

  .auth-logo h1 {
    font-size: 2rem;
  }

  .form-control {
    padding: 12px 15px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 12px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 25px 20px;
  }

  .auth-logo h1 {
    font-size: 1.8rem;
  }
}

/* Animation Classes for GSAP */
.fade-in {
  opacity: 0;
}

.slide-up {
  transform: translateY(30px);
  opacity: 0;
}

.scale-in {
  transform: scale(0.9);
  opacity: 0;
}

.slide-left {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right {
  transform: translateX(30px);
  opacity: 0;
}